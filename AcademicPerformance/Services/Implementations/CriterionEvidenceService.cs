using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Interfaces;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace AcademicPerformance.Services.Implementations;

/// <summary>
/// Service for handling criterion evidence file uploads
/// </summary>
public class CriterionEvidenceService : ICriterionEvidenceService
{
    private readonly IMinIOFileService _minioFileService;
    private readonly ISubmissionManager _submissionManager;
    private readonly IEvidenceFileStore _evidenceFileStore;
    private readonly IFileContentValidationService _fileContentValidationService;
    private readonly FileUploadOptions _fileUploadOptions;
    private readonly ILogger<CriterionEvidenceService> _logger;
    private readonly SemaphoreSlim _uploadSemaphore;

    // Progress tracking
    private readonly ConcurrentDictionary<string, UploadProgressInfo> _uploadProgress = new();

    public CriterionEvidenceService(
        IMinIOFileService minioFileService,
        ISubmissionManager submissionManager,
        IEvidenceFileStore evidenceFileStore,
        IFileContentValidationService fileContentValidationService,
        IOptions<FileUploadOptions> fileUploadOptions,
        ILogger<CriterionEvidenceService> logger)
    {
        _minioFileService = minioFileService;
        _submissionManager = submissionManager;
        _evidenceFileStore = evidenceFileStore;
        _fileContentValidationService = fileContentValidationService;
        _fileUploadOptions = fileUploadOptions.Value;
        _logger = logger;
        _uploadSemaphore = new SemaphoreSlim(_fileUploadOptions.MaxConcurrentUploads);
    }

    public async Task<CriterionEvidenceUploadResult> UploadSingleEvidenceAsync(UploadCriterionEvidenceRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var uploadId = request.UploadId ?? Guid.NewGuid().ToString();

        try
        {
            _logger.LogInformation("Starting single evidence upload: {UploadId}, User: {UserId}, File: {FileName}",
                uploadId, request.UserId, request.File.FileName);

            // Initialize progress tracking
            var progressInfo = new UploadProgressInfo
            {
                UploadId = uploadId,
                TotalFiles = 1,
                TotalBytes = request.File.Length,
                Status = "Processing",
                StartedAt = DateTime.UtcNow,
                CurrentlyProcessing = { request.File.FileName }
            };
            _uploadProgress[uploadId] = progressInfo;

            // Validate submission access
            var submission = await _submissionManager.GetSubmissionByIdAsync(request.UserId, request.SubmissionId);
            if (submission == null)
            {
                return new CriterionEvidenceUploadResult
                {
                    Success = false,
                    ErrorMessage = "Submission not found or access denied",
                    FileName = request.File.FileName
                };
            }

            // Validate file
            var validationResult = await ValidateFileAsync(request.File);
            if (!validationResult.IsValid)
            {
                progressInfo.Status = "Failed";
                progressInfo.Errors.AddRange(validationResult.Errors);
                progressInfo.CompletedAt = DateTime.UtcNow;

                return new CriterionEvidenceUploadResult
                {
                    Success = false,
                    ValidationErrors = validationResult.Errors,
                    ValidationResult = validationResult,
                    FileName = request.File.FileName
                };
            }

            // Upload file to MinIO
            await _uploadSemaphore.WaitAsync();
            try
            {
                using var stream = request.File.OpenReadStream();
                var objectName = _minioFileService.GenerateUniqueObjectName(request.File.FileName, "evidence");

                var uploadResult = await _minioFileService.UploadFileAsync(
                    "apdys-evidence-files", // TODO: Get from configuration
                    objectName,
                    stream,
                    request.File.ContentType);

                if (!uploadResult.Success)
                {
                    throw new InvalidOperationException($"File upload failed: {uploadResult.ErrorMessage}");
                }

                // Create criterion data entry
                var criterionDataDto = new CriterionDataInputDto
                {
                    DataEntries = new List<CriterionDataEntryDto>
                    {
                        new CriterionDataEntryDto
                        {
                            FieldName = "EvidenceFile",
                            FieldType = "File",
                            Value = request.File.FileName,
                            Description = request.Description ?? $"Evidence file: {request.File.FileName}",
                            CreatedAt = DateTime.UtcNow,
                            FileId = uploadResult.ObjectName
                        }
                    },
                    Notes = $"File uploaded: {request.File.FileName} ({request.File.Length} bytes)"
                };

                // Create evidence file record
                var evidenceFile = new EvidenceFileEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    AcademicSubmissionAutoIncrementId = 0, // TODO: Get actual submission auto increment ID
                    FormCriterionLinkId = request.CriterionLinkId,
                    FileName = request.File.FileName,
                    OriginalFileName = request.File.FileName,
                    StoredFilePath = objectName,
                    SizeBytes = request.File.Length,
                    ContentType = request.File.ContentType,
                    UploadedAt = DateTime.UtcNow,
                    UploadedByUniveristyUserId = request.UserId,
                    Description = request.Description,
                    MinioBucketName = "apdys-evidence-files",
                    MinioObjectName = objectName,
                    MinioETag = uploadResult.ETag,
                    StorageType = "MinIO",
                    FileChecksum = null // TODO: Calculate checksum
                };

                // Save evidence file to database
                var savedEvidenceFile = await _evidenceFileStore.CreateEvidenceFileAsync(evidenceFile);

                // Save criterion data
                var criterionSuccess = await _submissionManager.InputCriterionDataAsync(
                    request.UserId,
                    submission.FormId,
                    request.CriterionLinkId,
                    criterionDataDto);

                if (!criterionSuccess)
                {
                    // Cleanup uploaded file and database record
                    await _minioFileService.DeleteFileAsync("apdys-evidence-files", objectName);
                    await _evidenceFileStore.DeleteEvidenceFileAsync(savedEvidenceFile.Id);
                    throw new InvalidOperationException("Failed to save criterion data");
                }

                // Update progress
                progressInfo.ProcessedFiles = 1;
                progressInfo.SuccessfulFiles = 1;
                progressInfo.ProcessedBytes = request.File.Length;
                progressInfo.Status = "Completed";
                progressInfo.CompletedAt = DateTime.UtcNow;
                progressInfo.CurrentlyProcessing.Clear();

                _logger.LogInformation("Single evidence upload completed: {UploadId}, Duration: {Duration}ms",
                    uploadId, stopwatch.ElapsedMilliseconds);

                return new CriterionEvidenceUploadResult
                {
                    Success = true,
                    FileId = savedEvidenceFile.Id,
                    FileName = request.File.FileName,
                    FileSize = request.File.Length,
                    ContentType = request.File.ContentType,
                    UploadedAt = savedEvidenceFile.UploadedAt,
                    CriterionLinkId = request.CriterionLinkId,
                    ValidationResult = validationResult
                };
            }
            finally
            {
                _uploadSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in single evidence upload: {UploadId}", uploadId);

            // Update progress with error
            if (_uploadProgress.TryGetValue(uploadId, out var progressInfo))
            {
                progressInfo.Status = "Failed";
                progressInfo.FailedFiles = 1;
                progressInfo.CompletedAt = DateTime.UtcNow;
                progressInfo.Errors.Add(ex.Message);
                progressInfo.CurrentlyProcessing.Clear();
            }

            return new CriterionEvidenceUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                FileName = request.File.FileName
            };
        }
    }

    public async Task<BulkCriterionEvidenceUploadResult> UploadBulkEvidenceAsync(BulkUploadCriterionEvidenceRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var uploadId = request.UploadId ?? Guid.NewGuid().ToString();

        try
        {
            _logger.LogInformation("Starting bulk evidence upload: {UploadId}, User: {UserId}, FileCount: {FileCount}",
                uploadId, request.UserId, request.Files.Count);

            // Validate file count
            if (request.Files.Count > _fileUploadOptions.MaxFilesPerBulkUpload)
            {
                return new BulkCriterionEvidenceUploadResult
                {
                    Success = false,
                    TotalFiles = request.Files.Count,
                    GeneralErrors = { $"Too many files. Maximum allowed: {_fileUploadOptions.MaxFilesPerBulkUpload}" }
                };
            }

            // Initialize progress tracking
            var progressInfo = new UploadProgressInfo
            {
                UploadId = uploadId,
                TotalFiles = request.Files.Count,
                TotalBytes = request.Files.Sum(f => f.Length),
                Status = "Processing",
                StartedAt = DateTime.UtcNow
            };
            _uploadProgress[uploadId] = progressInfo;

            // Validate submission access
            var submission = await _submissionManager.GetSubmissionByIdAsync(request.UserId, request.SubmissionId);
            if (submission == null)
            {
                return new BulkCriterionEvidenceUploadResult
                {
                    Success = false,
                    TotalFiles = request.Files.Count,
                    GeneralErrors = { "Submission not found or access denied" }
                };
            }

            // Process files in parallel with controlled concurrency
            var results = new ConcurrentBag<CriterionEvidenceUploadResult>();
            var semaphore = new SemaphoreSlim(_fileUploadOptions.MaxConcurrentUploads);

            var tasks = request.Files.Select(async file =>
            {
                await semaphore.WaitAsync();
                try
                {
                    // Get criterion mapping for this file
                    var criterionLinkId = request.CriterionMappings.GetValueOrDefault(file.FileName, "");
                    if (string.IsNullOrEmpty(criterionLinkId))
                    {
                        return new CriterionEvidenceUploadResult
                        {
                            Success = false,
                            FileName = file.FileName,
                            ErrorMessage = "No criterion mapping found for file"
                        };
                    }

                    // Create single upload request
                    var singleRequest = new UploadCriterionEvidenceRequest
                    {
                        UserId = request.UserId,
                        SubmissionId = request.SubmissionId,
                        CriterionLinkId = criterionLinkId,
                        File = file,
                        Description = request.Description,
                        UploadId = $"{uploadId}_{file.FileName}"
                    };

                    // Update progress
                    progressInfo.CurrentlyProcessing.Add(file.FileName);

                    var result = await UploadSingleEvidenceAsync(singleRequest);

                    // Update progress
                    progressInfo.CurrentlyProcessing.Remove(file.FileName);
                    progressInfo.ProcessedFiles++;
                    progressInfo.ProcessedBytes += file.Length;

                    if (result.Success)
                        progressInfo.SuccessfulFiles++;
                    else
                        progressInfo.FailedFiles++;

                    return result;
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var uploadResults = await Task.WhenAll(tasks);

            // Update final progress
            progressInfo.Status = "Completed";
            progressInfo.CompletedAt = DateTime.UtcNow;
            progressInfo.CurrentlyProcessing.Clear();

            var bulkResult = new BulkCriterionEvidenceUploadResult
            {
                Success = uploadResults.Any(r => r.Success),
                TotalFiles = request.Files.Count,
                SuccessfulUploads = uploadResults.Count(r => r.Success),
                FailedUploads = uploadResults.Count(r => !r.Success),
                Results = uploadResults.ToList(),
                TotalProcessingTime = stopwatch.Elapsed,
                UploadId = uploadId
            };

            _logger.LogInformation("Bulk evidence upload completed: {UploadId}, Success: {SuccessCount}/{TotalCount}, Duration: {Duration}ms",
                uploadId, bulkResult.SuccessfulUploads, bulkResult.TotalFiles, stopwatch.ElapsedMilliseconds);

            return bulkResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in bulk evidence upload: {UploadId}", uploadId);

            // Update progress with error
            if (_uploadProgress.TryGetValue(uploadId, out var progressInfo))
            {
                progressInfo.Status = "Failed";
                progressInfo.CompletedAt = DateTime.UtcNow;
                progressInfo.Errors.Add(ex.Message);
                progressInfo.CurrentlyProcessing.Clear();
            }

            return new BulkCriterionEvidenceUploadResult
            {
                Success = false,
                TotalFiles = request.Files.Count,
                GeneralErrors = { ex.Message },
                TotalProcessingTime = stopwatch.Elapsed,
                UploadId = uploadId
            };
        }
    }

    public async Task<FileValidationResult> ValidateFileAsync(IFormFile file)
    {
        try
        {
            var result = new FileValidationResult();

            // Basic validation
            if (file == null || file.Length == 0)
            {
                result.AddError("File is required and cannot be empty");
                return result;
            }

            // File size validation
            result.FileSize = file.Length;
            if (file.Length > _fileUploadOptions.MaxFileSize)
            {
                result.AddError($"File size exceeds maximum allowed size of {_fileUploadOptions.GetMaxFileSizeFormatted()}");
            }

            // Extension validation
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!_fileUploadOptions.IsExtensionAllowed(extension))
            {
                result.AddError($"File extension '{extension}' is not allowed");
            }

            // MIME type validation
            if (!_fileUploadOptions.IsMimeTypeAllowed(file.ContentType))
            {
                result.AddError($"File type '{file.ContentType}' is not allowed");
            }

            result.DetectedContentType = file.ContentType;
            result.DetectedExtension = extension;

            // Advanced content validation with magic numbers
            if (_fileUploadOptions.EnableMagicNumberValidation)
            {
                var contentValidation = await _fileContentValidationService.ValidateFileContentAsync(file);
                if (!contentValidation.IsValid)
                {
                    result.Errors.AddRange(contentValidation.Errors);
                }

                result.PassedMagicNumberCheck = contentValidation.PassedMagicNumberCheck;
            }

            // Use existing MinIO validation for additional checks
            using var stream = file.OpenReadStream();
            var minioValidation = await _minioFileService.ValidateFileAsync(stream, file.FileName, file.ContentType);

            if (!minioValidation.IsValid)
            {
                result.Errors.AddRange(minioValidation.Errors);
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating file: {FileName}", file.FileName);
            return FileValidationResult.Failure($"Validation error: {ex.Message}");
        }
    }

    public async Task<Dictionary<string, FileValidationResult>> ValidateFilesAsync(IEnumerable<IFormFile> files)
    {
        var results = new Dictionary<string, FileValidationResult>();

        foreach (var file in files)
        {
            var result = await ValidateFileAsync(file);
            results[file.FileName] = result;
        }

        return results;
    }

    public async Task<UploadProgressInfo?> GetUploadProgressAsync(string uploadId)
    {
        await Task.CompletedTask; // Make async for future enhancements
        return _uploadProgress.TryGetValue(uploadId, out var progress) ? progress : null;
    }

    public void Dispose()
    {
        _uploadSemaphore?.Dispose();
    }
}
