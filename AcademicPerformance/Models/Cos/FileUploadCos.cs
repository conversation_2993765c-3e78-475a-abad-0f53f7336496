using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Cos;

/// <summary>
/// Command object for uploading evidence file
/// </summary>
public class UploadEvidenceFileCo
{
    [Required]
    public required IFormFile File { get; set; }

    [Required]
    public int AcademicSubmissionId { get; set; }

    public string? FormCriterionLinkId { get; set; }

    public string? SubmittedDynamicDataInstanceId { get; set; }

    public string? Description { get; set; }
}

/// <summary>
/// Command object for uploading multiple evidence files
/// </summary>
public class UploadMultipleEvidenceFilesCo
{
    [Required]
    public required List<IFormFile> Files { get; set; }

    [Required]
    public int AcademicSubmissionId { get; set; }

    public string? FormCriterionLinkId { get; set; }

    public string? SubmittedDynamicDataInstanceId { get; set; }

    public string? Description { get; set; }
}

/// <summary>
/// Command object for uploading temporary file
/// </summary>
public class UploadTemporaryFileCo
{
    [Required]
    public required IFormFile File { get; set; }

    public string? Description { get; set; }
}

/// <summary>
/// Command object for uploading permanent file directly
/// </summary>
public class UploadPermanentFileCo
{
    [Required]
    public required IFormFile File { get; set; }

    [Required]
    public int AcademicSubmissionId { get; set; }

    public string? FormCriterionLinkId { get; set; }

    public string? SubmittedDynamicDataInstanceId { get; set; }

    public string? Description { get; set; }
}

/// <summary>
/// Command object for converting temporary file to permanent
/// </summary>
public class ConvertTemporaryToPermanentCo
{
    [Required]
    public required string TempId { get; set; }

    [Required]
    public int AcademicSubmissionId { get; set; }

    public string? FormCriterionLinkId { get; set; }

    public string? SubmittedDynamicDataInstanceId { get; set; }

    public string? Description { get; set; }
}

/// <summary>
/// Command object for bulk deleting evidence files
/// </summary>
public class BulkDeleteEvidenceFilesCo
{
    [Required]
    public required List<string> EvidenceFileIds { get; set; }
}

/// <summary>
/// Command object for getting evidence files by submission with pagination
/// </summary>
public class GetEvidenceFilesBySubmissionCo
{
    [Required]
    public int AcademicSubmissionId { get; set; }
}

/// <summary>
/// Command object for getting evidence files with pagination
/// </summary>
public class GetEvidenceFilesCo
{
    public string? SearchTerm { get; set; }

    public int? AcademicSubmissionId { get; set; }

    public string? ContentType { get; set; }

    public DateTime? UploadedAfter { get; set; }

    public DateTime? UploadedBefore { get; set; }

    public string? UploadedByUserId { get; set; }

    public string? StorageType { get; set; }
}
