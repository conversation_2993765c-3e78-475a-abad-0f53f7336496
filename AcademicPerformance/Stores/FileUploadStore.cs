using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Mapster;

namespace AcademicPerformance.Stores;

/// <summary>
/// FileUpload data access store implementation
/// </summary>
public class FileUploadStore : IFileUploadStore
{
    private readonly AcademicPerformanceDbContext _context;

    public FileUploadStore(AcademicPerformanceDbContext context)
    {
        _context = context;
    }

    #region Evidence File CRUD Operations

    public async Task<EvidenceFileEntity> CreateEvidenceFileAsync(EvidenceFileEntity evidenceFile, bool saveChanges = false)
    {
        _context.EvidenceFiles.Add(evidenceFile);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return evidenceFile;
    }

    public async Task<IEnumerable<EvidenceFileEntity>> CreateEvidenceFilesAsync(IEnumerable<EvidenceFileEntity> evidenceFiles, bool saveChanges = false)
    {
        _context.EvidenceFiles.AddRange(evidenceFiles);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return evidenceFiles;
    }

    public async Task<EvidenceFileEntity> UpdateEvidenceFileAsync(EvidenceFileEntity evidenceFile, bool saveChanges = false)
    {
        _context.EvidenceFiles.Update(evidenceFile);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return evidenceFile;
    }

    public async Task<EvidenceFileEntity?> GetEvidenceFileByIdAsync(string evidenceFileId)
    {
        return await _context.EvidenceFiles
            .Include(f => f.AcademicSubmission)
            .FirstOrDefaultAsync(f => f.Id == evidenceFileId);
    }

    public async Task<EvidenceFileEntity?> GetEvidenceFileForUpdateAsync(string evidenceFileId)
    {
        return await _context.EvidenceFiles
            .FirstOrDefaultAsync(f => f.Id == evidenceFileId);
    }

    public async Task<IEnumerable<EvidenceFileEntity>> GetEvidenceFilesByIdsAsync(IEnumerable<string> evidenceFileIds)
    {
        return await _context.EvidenceFiles
            .Where(f => evidenceFileIds.Contains(f.Id))
            .ToListAsync();
    }

    public async Task<bool> DeleteEvidenceFileAsync(string evidenceFileId, bool saveChanges = false)
    {
        var evidenceFile = await GetEvidenceFileForUpdateAsync(evidenceFileId);
        if (evidenceFile == null)
        {
            return false;
        }

        _context.EvidenceFiles.Remove(evidenceFile);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return true;
    }

    public async Task<bool> DeleteEvidenceFilesAsync(IEnumerable<string> evidenceFileIds, bool saveChanges = false)
    {
        var evidenceFiles = await GetEvidenceFilesByIdsAsync(evidenceFileIds);
        if (!evidenceFiles.Any())
        {
            return false;
        }

        _context.EvidenceFiles.RemoveRange(evidenceFiles);
        if (saveChanges)
        {
            await _context.SaveChangesAsync();
        }
        return true;
    }

    #endregion

    #region Query Operations with Pagination

    public async Task<PagedListDto<EvidenceFileDto>> GetEvidenceFilesBySubmissionAsync(PagedListCo<GetEvidenceFilesBySubmissionCo> co)
    {
        var query = _context.EvidenceFiles
            .Where(f => f.AcademicSubmissionAutoIncrementId == co.Data.AcademicSubmissionId)
            .OrderByDescending(f => f.UploadedAt);

        var totalCount = await query.CountAsync();
        
        var items = await query
            .Skip((co.PageNumber - 1) * co.PageSize)
            .Take(co.PageSize)
            .Select(f => new EvidenceFileDto
            {
                Id = f.Id,
                AutoIncrementId = f.AutoIncrementId,
                AcademicSubmissionAutoIncrementId = f.AcademicSubmissionAutoIncrementId,
                FormCriterionLinkId = f.FormCriterionLinkId,
                SubmittedDynamicDataInstanceId = f.SubmittedDynamicDataInstanceId,
                FileName = f.FileName,
                OriginalFileName = f.OriginalFileName ?? f.FileName,
                SizeBytes = f.SizeBytes,
                ContentType = f.ContentType,
                UploadedAt = f.UploadedAt,
                UploadedByUserId = f.UploadedByUniveristyUserId ?? string.Empty,
                Description = f.Description,
                StorageType = f.StorageType,
                MinioBucketName = f.MinioBucketName,
                MinioObjectName = f.MinioObjectName,
                MinioETag = f.MinioETag,
                FileChecksum = f.FileChecksum,
                AccessUrl = f.AccessUrl,
                PresignedUrlExpiry = f.MinioPresignedUrlExpiry
            })
            .ToListAsync();

        return new PagedListDto<EvidenceFileDto>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = co.PageNumber,
            PageSize = co.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / co.PageSize)
        };
    }

    public async Task<PagedListDto<EvidenceFileDto>> GetEvidenceFilesAsync(PagedListCo<GetEvidenceFilesCo> co)
    {
        var query = _context.EvidenceFiles.AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(co.Data.SearchTerm))
        {
            query = query.Where(f => f.FileName.Contains(co.Data.SearchTerm) || 
                                   f.Description!.Contains(co.Data.SearchTerm));
        }

        if (co.Data.AcademicSubmissionId.HasValue)
        {
            query = query.Where(f => f.AcademicSubmissionAutoIncrementId == co.Data.AcademicSubmissionId.Value);
        }

        if (!string.IsNullOrEmpty(co.Data.ContentType))
        {
            query = query.Where(f => f.ContentType == co.Data.ContentType);
        }

        if (co.Data.UploadedAfter.HasValue)
        {
            query = query.Where(f => f.UploadedAt >= co.Data.UploadedAfter.Value);
        }

        if (co.Data.UploadedBefore.HasValue)
        {
            query = query.Where(f => f.UploadedAt <= co.Data.UploadedBefore.Value);
        }

        if (!string.IsNullOrEmpty(co.Data.UploadedByUserId))
        {
            query = query.Where(f => f.UploadedByUniveristyUserId == co.Data.UploadedByUserId);
        }

        if (!string.IsNullOrEmpty(co.Data.StorageType))
        {
            query = query.Where(f => f.StorageType == co.Data.StorageType);
        }

        query = query.OrderByDescending(f => f.UploadedAt);

        var totalCount = await query.CountAsync();
        
        var items = await query
            .Skip((co.PageNumber - 1) * co.PageSize)
            .Take(co.PageSize)
            .Adapt<List<EvidenceFileDto>>();

        return new PagedListDto<EvidenceFileDto>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = co.PageNumber,
            PageSize = co.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / co.PageSize)
        };
    }

    #endregion

    #region Academic Submission Operations

    public async Task<AcademicSubmissionEntity?> GetAcademicSubmissionByIdAsync(int academicSubmissionId)
    {
        return await _context.AcademicSubmissions
            .FirstOrDefaultAsync(s => s.AutoIncrementId == academicSubmissionId);
    }

    public async Task<bool> AcademicSubmissionExistsAsync(int academicSubmissionId)
    {
        return await _context.AcademicSubmissions
            .AnyAsync(s => s.AutoIncrementId == academicSubmissionId);
    }

    #endregion

    #region Statistics Operations

    public async Task<int> GetEvidenceFileCountAsync()
    {
        return await _context.EvidenceFiles.CountAsync();
    }

    public async Task<long> GetTotalFileSizeAsync()
    {
        return await _context.EvidenceFiles.SumAsync(f => f.SizeBytes);
    }

    public async Task<int> GetEvidenceFileCountBySubmissionAsync(int academicSubmissionId)
    {
        return await _context.EvidenceFiles
            .CountAsync(f => f.AcademicSubmissionAutoIncrementId == academicSubmissionId);
    }

    public async Task<IEnumerable<EvidenceFileEntity>> GetEvidenceFilesBySubmissionForDeleteAsync(int academicSubmissionId)
    {
        return await _context.EvidenceFiles
            .Where(f => f.AcademicSubmissionAutoIncrementId == academicSubmissionId)
            .ToListAsync();
    }

    #endregion

    #region Bulk Operations

    public async Task<int> BulkDeleteEvidenceFilesBySubmissionAsync(int academicSubmissionId, bool saveChanges = false)
    {
        var evidenceFiles = await GetEvidenceFilesBySubmissionForDeleteAsync(academicSubmissionId);
        var count = evidenceFiles.Count();
        
        if (count > 0)
        {
            _context.EvidenceFiles.RemoveRange(evidenceFiles);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
        }
        
        return count;
    }

    #endregion

    #region Transaction Support

    public async Task SaveChangesAsync()
    {
        await _context.SaveChangesAsync();
    }

    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await operation();
            await transaction.CommitAsync();
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    #endregion
}
