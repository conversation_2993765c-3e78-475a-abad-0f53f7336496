using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces;

/// <summary>
/// FileUpload business logic manager interface
/// </summary>
public interface IFileUploadManager
{
    // Upload Operations
    Task<EvidenceFileDto> UploadEvidenceFileAsync(UploadEvidenceFileCo co);
    Task<BulkUploadResultDto> UploadMultipleEvidenceFilesAsync(UploadMultipleEvidenceFilesCo co);
    Task<TemporaryFileDto> UploadTemporaryFileAsync(UploadTemporaryFileCo co);
    Task<EvidenceFileDto> UploadPermanentFileAsync(UploadPermanentFileCo co);
    
    // Download Operations
    Task<FileDownloadDto> DownloadEvidenceFileAsync(string evidenceFileId);
    Task<PresignedUrlDto> GeneratePresignedUrlAsync(string evidenceFileId, int expirySeconds = 3600);
    Task<EvidenceFileMetadataDto> GetEvidenceFileMetadataAsync(string evidenceFileId);
    
    // List Operations
    Task<PagedListDto<EvidenceFileDto>> GetEvidenceFilesBySubmissionAsync(PagedListCo<GetEvidenceFilesBySubmissionCo> co);
    
    // Conversion Operations
    Task<EvidenceFileDto> ConvertTemporaryToPermanentAsync(ConvertTemporaryToPermanentCo co);
    
    // Delete Operations
    Task<bool> DeleteEvidenceFileAsync(string evidenceFileId);
    Task<BulkDeleteResultDto> BulkDeleteEvidenceFilesAsync(BulkDeleteEvidenceFilesCo co);
    Task<bool> DeleteTemporaryFileAsync(string tempId);
    Task<BulkDeleteResultDto> DeleteAllEvidenceFilesBySubmissionAsync(int academicSubmissionId);
}
