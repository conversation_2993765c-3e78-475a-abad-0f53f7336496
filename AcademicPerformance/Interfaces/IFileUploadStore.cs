using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces;

/// <summary>
/// FileUpload data access store interface
/// </summary>
public interface IFileUploadStore
{
    // Evidence File CRUD Operations
    Task<EvidenceFileEntity> CreateEvidenceFileAsync(EvidenceFileEntity evidenceFile, bool saveChanges = false);
    Task<IEnumerable<EvidenceFileEntity>> CreateEvidenceFilesAsync(IEnumerable<EvidenceFileEntity> evidenceFiles, bool saveChanges = false);
    Task<EvidenceFileEntity> UpdateEvidenceFileAsync(EvidenceFileEntity evidenceFile, bool saveChanges = false);
    Task<EvidenceFileEntity?> GetEvidenceFileByIdAsync(string evidenceFileId);
    Task<EvidenceFileEntity?> GetEvidenceFileForUpdateAsync(string evidenceFileId);
    Task<IEnumerable<EvidenceFileEntity>> GetEvidenceFilesByIdsAsync(IEnumerable<string> evidenceFileIds);
    Task<bool> DeleteEvidenceFileAsync(string evidenceFileId, bool saveChanges = false);
    Task<bool> DeleteEvidenceFilesAsync(IEnumerable<string> evidenceFileIds, bool saveChanges = false);
    
    // Query Operations with Pagination
    Task<PagedListDto<EvidenceFileDto>> GetEvidenceFilesBySubmissionAsync(PagedListCo<GetEvidenceFilesBySubmissionCo> co);
    Task<PagedListDto<EvidenceFileDto>> GetEvidenceFilesAsync(PagedListCo<GetEvidenceFilesCo> co);
    
    // Academic Submission Operations
    Task<AcademicSubmissionEntity?> GetAcademicSubmissionByIdAsync(int academicSubmissionId);
    Task<bool> AcademicSubmissionExistsAsync(int academicSubmissionId);
    
    // Statistics Operations
    Task<int> GetEvidenceFileCountAsync();
    Task<long> GetTotalFileSizeAsync();
    Task<int> GetEvidenceFileCountBySubmissionAsync(int academicSubmissionId);
    Task<IEnumerable<EvidenceFileEntity>> GetEvidenceFilesBySubmissionForDeleteAsync(int academicSubmissionId);
    
    // Bulk Operations
    Task<int> BulkDeleteEvidenceFilesBySubmissionAsync(int academicSubmissionId, bool saveChanges = false);
    
    // Transaction Support
    Task SaveChangesAsync();
    Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation);
}
